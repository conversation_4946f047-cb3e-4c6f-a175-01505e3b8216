//? File where we Initialize all the sampleListing data

//requiring mongoose
const data = require("./data.js");
//requiring listing.js
const Listing = require("../models/listing.js");

//*------------------------------------- Mongoose Setup
const mongoose = require('mongoose');
main()
    .then(() => {
        console.log('Connected to MongoDB');
    })
    .catch(err => console.log(err));
    
async function main() {
    await mongoose.connect('mongodb://127.0.0.1:27017/WanderLust');
}

//* ------------------------------------- Function to initialize the database with data
const initDB = async()=> { 
    await Listing.deleteMany({}); //Cleaning database before inserting data
    await Listing.insertMany(data.data);
    console.log("Data was initialized!");
};
initDB();
