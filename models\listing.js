//? File containing Schema and model 

//Requring mongoose 
const mongoose = require('mongoose');
//so that we can use Schema instead of mongoose.schema
const Schema = mongoose.Schema;

//Creating a new Schema named listingSchema
const listingSchema = new Schema ({
    title: {type: String, required: true,}, 
    description: String,
    image: {
        filename: {
            type: String,
            default: "listingimage"
        },
        url: {
            type: String,
            default: "/images/default.png"
        }
    },
    price: Number,
    location: String,
    country: String
});

//Defining a model named Listing which is a constructor that will help us to create documents that follow listingSchema
const Listing = mongoose.model("Listing", listingSchema);
//Exporting Listing model 
module.exports = Listing;
